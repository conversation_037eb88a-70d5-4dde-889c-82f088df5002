<!-- HAMBURGER MENU ICON FOR HEADER CODE - COPY THIS TO REPLAY HAWK HEADER CODE -->

<style>
/* Hamburger Menu Icon Styling */
.hamburger-menu {
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 9999;
  cursor: pointer;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.hamburger-menu:hover {
  background: #f8f9fa;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Hamburger Icon Lines */
.hamburger-icon {
  width: 24px;
  height: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hamburger-line {
  width: 100%;
  height: 3px;
  background-color: #333;
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* Active state animation */
.hamburger-menu.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-menu.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger-menu.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Responsive - hide on larger screens if needed */
@media (min-width: 768px) {
  .hamburger-menu {
    display: none; /* Hide on desktop if you only want it on mobile */
  }
}

/* Show on mobile screens */
@media (max-width: 767px) {
  .hamburger-menu {
    display: block;
  }
}
</style>

<!-- Hamburger Menu HTML -->
<div class="hamburger-menu" id="hamburgerMenu" onclick="toggleSidebar()">
  <div class="hamburger-icon">
    <div class="hamburger-line"></div>
    <div class="hamburger-line"></div>
    <div class="hamburger-line"></div>
  </div>
</div>

<script>
// Hamburger Menu Functionality
function toggleSidebar() {
  const hamburger = document.getElementById('hamburgerMenu');
  const sidebar = document.getElementById('playlist-container');
  
  // Toggle hamburger animation
  hamburger.classList.toggle('active');
  
  // Toggle sidebar visibility
  if (sidebar) {
    if (sidebar.style.display === 'none' || sidebar.style.display === '') {
      sidebar.style.display = 'block';
      sidebar.style.transform = 'translateX(0)';
    } else {
      sidebar.style.display = 'none';
      sidebar.style.transform = 'translateX(-100%)';
    }
  }
}

// Close sidebar when clicking outside (optional)
document.addEventListener('click', function(event) {
  const hamburger = document.getElementById('hamburgerMenu');
  const sidebar = document.getElementById('playlist-container');
  
  if (!hamburger.contains(event.target) && !sidebar.contains(event.target)) {
    if (window.innerWidth <= 767) { // Only on mobile
      sidebar.style.display = 'none';
      hamburger.classList.remove('active');
    }
  }
});

// Handle window resize
window.addEventListener('resize', function() {
  const sidebar = document.getElementById('playlist-container');
  const hamburger = document.getElementById('hamburgerMenu');
  
  if (window.innerWidth > 767) {
    // Show sidebar on desktop
    if (sidebar) {
      sidebar.style.display = 'block';
      sidebar.style.transform = 'translateX(0)';
    }
    if (hamburger) {
      hamburger.classList.remove('active');
    }
  }
});
</script>
