// DROPDOWN FUNCTIONALITY FOR SIDEBAR
// Copy this JavaScript code and add to your HTML page or Replay Hawk platform

document.addEventListener('DOMContentLoaded', function() {
  console.log('Dropdown functionality loaded');
  
  // Add click event to all category items with sub-lessons
  const categoryItems = document.querySelectorAll('.category-item-container');
  
  console.log('Found category items:', categoryItems.length);
  
  categoryItems.forEach((item, index) => {
    const subLessons = item.querySelector('.sub-cat-bg');
    
    if (subLessons) {
      console.log(`Category item ${index} has sub-lessons`);
      
      const clickableArea = item.querySelector('.flex.items-center.justify-between');
      
      if (clickableArea) {
        // Make it visually clear it's clickable
        clickableArea.style.cursor = 'pointer';
        
        // Add click event listener
        clickableArea.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          
          console.log(`Toggling category item ${index}`);
          
          // Toggle expanded class
          item.classList.toggle('expanded');
          
          // Optional: Close other expanded items (accordion behavior)
          // Uncomment the lines below if you want only one section open at a time
          /*
          categoryItems.forEach(otherItem => {
            if (otherItem !== item) {
              otherItem.classList.remove('expanded');
            }
          });
          */
        });
        
        console.log(`Added click listener to category item ${index}`);
      }
    }
  });
  
  // Alternative method if the above doesn't work
  // This targets the specific class structure you showed
  const alternativeItems = document.querySelectorAll('div.sub-cat-bg.py-1.flex.items-center.justify-between.pr-2');
  
  if (alternativeItems.length > 0) {
    console.log('Using alternative targeting method');
    
    alternativeItems.forEach((item, index) => {
      const parentContainer = item.closest('.category-item-container') || item.parentElement;
      
      if (parentContainer) {
        const clickableArea = parentContainer.querySelector('.flex.items-center.justify-between');
        
        if (clickableArea && !clickableArea.hasAttribute('data-dropdown-added')) {
          clickableArea.setAttribute('data-dropdown-added', 'true');
          clickableArea.style.cursor = 'pointer';
          
          clickableArea.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            parentContainer.classList.toggle('expanded');
            console.log(`Alternative method: Toggled item ${index}`);
          });
        }
      }
    });
  }
});

// Function to manually toggle a specific item (for testing)
function toggleDropdown(itemIndex) {
  const categoryItems = document.querySelectorAll('.category-item-container');
  if (categoryItems[itemIndex]) {
    categoryItems[itemIndex].classList.toggle('expanded');
    console.log(`Manually toggled item ${itemIndex}`);
  }
}

// Function to expand all dropdowns
function expandAllDropdowns() {
  const categoryItems = document.querySelectorAll('.category-item-container');
  categoryItems.forEach(item => {
    item.classList.add('expanded');
  });
  console.log('Expanded all dropdowns');
}

// Function to collapse all dropdowns
function collapseAllDropdowns() {
  const categoryItems = document.querySelectorAll('.category-item-container');
  categoryItems.forEach(item => {
    item.classList.remove('expanded');
  });
  console.log('Collapsed all dropdowns');
}

// Export functions for global access (if needed)
window.toggleDropdown = toggleDropdown;
window.expandAllDropdowns = expandAllDropdowns;
window.collapseAllDropdowns = collapseAllDropdowns;
