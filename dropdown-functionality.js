// DROPDOWN FUNCTIONALITY FOR SIDEBAR
// Copy this JavaScript code and add to your HTML page or Replay Hawk platform

document.addEventListener('DOMContentLoaded', function() {
  console.log('Dropdown functionality loaded');

  // First, target the specific TIR Lessons container
  const tirLessonsContainer = document.querySelector('div.sub-cat-bg.py-1.flex.items-center.justify-between.pr-2');

  if (tirLessonsContainer) {
    console.log('Found TIR Lessons container');

    // Find the parent container that contains the sub-lessons
    const parentContainer = tirLessonsContainer.closest('div');

    if (parentContainer) {
      // Find the actual sub-lessons list (next sibling or child)
      const subLessonsList = parentContainer.querySelector('.sub-cat-bg:not(.py-1)') ||
                            parentContainer.nextElementSibling;

      if (subLessonsList) {
        // Initially hide the sub-lessons
        subLessonsList.style.display = 'none';

        // Add click event to the container
        tirLessonsContainer.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          // Toggle the display of sub-lessons and arrow rotation
          if (subLessonsList.style.display === 'none' || subLessonsList.style.display === '') {
            // Show dropdown and rotate arrow to down (▼)
            subLessonsList.style.display = 'block';
            parentContainer.classList.add('expanded');
            console.log('Showed TIR Lessons list - Arrow rotated to ▼');
          } else {
            // Hide dropdown and rotate arrow to right (▶)
            subLessonsList.style.display = 'none';
            parentContainer.classList.remove('expanded');
            console.log('Hidden TIR Lessons list - Arrow rotated to ▶');
          }
        });

        console.log('Added click functionality to TIR Lessons');
      }
    }
  }

  // Add click event to all category items with sub-lessons
  const categoryItems = document.querySelectorAll('.category-item-container');

  console.log('Found category items:', categoryItems.length);

  categoryItems.forEach((item, index) => {
    const subLessons = item.querySelector('.sub-cat-bg');

    if (subLessons) {
      console.log(`Category item ${index} has sub-lessons`);

      const clickableArea = item.querySelector('.flex.items-center.justify-between');

      if (clickableArea) {
        // Add dropdown indicator classes
        item.classList.add('has-dropdown');
        clickableArea.setAttribute('data-has-dropdown', 'true');

        // Make it visually clear it's clickable
        clickableArea.style.cursor = 'pointer';

        // Add click event listener
        clickableArea.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          console.log(`Toggling category item ${index}`);

          // Toggle expanded class
          item.classList.toggle('expanded');

          // Optional: Close other expanded items (accordion behavior)
          // Uncomment the lines below if you want only one section open at a time
          /*
          categoryItems.forEach(otherItem => {
            if (otherItem !== item) {
              otherItem.classList.remove('expanded');
            }
          });
          */
        });

        console.log(`Added click listener to category item ${index}`);
      }
    }
  });

  // Alternative method if the above doesn't work
  // This targets any element that has sub-lessons
  const allItemsWithSubLessons = document.querySelectorAll('*');

  allItemsWithSubLessons.forEach((item, index) => {
    const subLessons = item.querySelector('.sub-cat-bg');

    if (subLessons && !item.hasAttribute('data-dropdown-processed')) {
      item.setAttribute('data-dropdown-processed', 'true');
      console.log(`Found item with sub-lessons using alternative method: ${index}`);

      const clickableArea = item.querySelector('.flex.items-center.justify-between');

      if (clickableArea && !clickableArea.hasAttribute('data-dropdown-added')) {
        // Add dropdown indicator classes
        item.classList.add('has-dropdown');
        clickableArea.setAttribute('data-has-dropdown', 'true');
        clickableArea.setAttribute('data-dropdown-added', 'true');
        clickableArea.style.cursor = 'pointer';

        clickableArea.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          item.classList.toggle('expanded');
          console.log(`Alternative method: Toggled item ${index}`);
        });

        console.log(`Alternative method: Added dropdown to item ${index}`);
      }
    }
  });
});

// Function to manually toggle a specific item (for testing)
function toggleDropdown(itemIndex) {
  const categoryItems = document.querySelectorAll('.category-item-container');
  if (categoryItems[itemIndex]) {
    categoryItems[itemIndex].classList.toggle('expanded');
    console.log(`Manually toggled item ${itemIndex}`);
  }
}

// Function to expand all dropdowns
function expandAllDropdowns() {
  const categoryItems = document.querySelectorAll('.category-item-container');
  categoryItems.forEach(item => {
    item.classList.add('expanded');
  });
  console.log('Expanded all dropdowns');
}

// Function to collapse all dropdowns
function collapseAllDropdowns() {
  const categoryItems = document.querySelectorAll('.category-item-container');
  categoryItems.forEach(item => {
    item.classList.remove('expanded');
  });
  console.log('Collapsed all dropdowns');
}

// Export functions for global access (if needed)
window.toggleDropdown = toggleDropdown;
window.expandAllDropdowns = expandAllDropdowns;
window.collapseAllDropdowns = collapseAllDropdowns;
