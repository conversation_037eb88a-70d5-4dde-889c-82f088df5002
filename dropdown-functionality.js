// DROPDOWN FUNCTIONALITY FOR SIDEBAR
// Copy this JavaScript code and add to your HTML page or Replay Hawk platform

document.addEventListener('DOMContentLoaded', function() {
  console.log('Dropdown functionality loaded');

  // First, target the specific TIR Lessons heading
  const tirLessonsHeading = document.querySelector('h6.mt-0.text-sm.font-medium.custom-word-break.m-0.px-4.sub-cat-title');

  if (tirLessonsHeading) {
    console.log('Found TIR Lessons heading');

    // Find the parent container
    const parentContainer = tirLessonsHeading.closest('div');

    if (parentContainer) {
      // Add click event to the heading
      tirLessonsHeading.style.cursor = 'pointer';
      tirLessonsHeading.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        parentContainer.classList.toggle('expanded');
        console.log('Toggled TIR Lessons dropdown');
      });
    }
  }

  // Add click event to all category items with sub-lessons
  const categoryItems = document.querySelectorAll('.category-item-container');

  console.log('Found category items:', categoryItems.length);

  categoryItems.forEach((item, index) => {
    const subLessons = item.querySelector('.sub-cat-bg');

    if (subLessons) {
      console.log(`Category item ${index} has sub-lessons`);

      const clickableArea = item.querySelector('.flex.items-center.justify-between');

      if (clickableArea) {
        // Add dropdown indicator classes
        item.classList.add('has-dropdown');
        clickableArea.setAttribute('data-has-dropdown', 'true');

        // Make it visually clear it's clickable
        clickableArea.style.cursor = 'pointer';

        // Add click event listener
        clickableArea.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          console.log(`Toggling category item ${index}`);

          // Toggle expanded class
          item.classList.toggle('expanded');

          // Optional: Close other expanded items (accordion behavior)
          // Uncomment the lines below if you want only one section open at a time
          /*
          categoryItems.forEach(otherItem => {
            if (otherItem !== item) {
              otherItem.classList.remove('expanded');
            }
          });
          */
        });

        console.log(`Added click listener to category item ${index}`);
      }
    }
  });

  // Alternative method if the above doesn't work
  // This targets any element that has sub-lessons
  const allItemsWithSubLessons = document.querySelectorAll('*');

  allItemsWithSubLessons.forEach((item, index) => {
    const subLessons = item.querySelector('.sub-cat-bg');

    if (subLessons && !item.hasAttribute('data-dropdown-processed')) {
      item.setAttribute('data-dropdown-processed', 'true');
      console.log(`Found item with sub-lessons using alternative method: ${index}`);

      const clickableArea = item.querySelector('.flex.items-center.justify-between');

      if (clickableArea && !clickableArea.hasAttribute('data-dropdown-added')) {
        // Add dropdown indicator classes
        item.classList.add('has-dropdown');
        clickableArea.setAttribute('data-has-dropdown', 'true');
        clickableArea.setAttribute('data-dropdown-added', 'true');
        clickableArea.style.cursor = 'pointer';

        clickableArea.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          item.classList.toggle('expanded');
          console.log(`Alternative method: Toggled item ${index}`);
        });

        console.log(`Alternative method: Added dropdown to item ${index}`);
      }
    }
  });
});

// Function to manually toggle a specific item (for testing)
function toggleDropdown(itemIndex) {
  const categoryItems = document.querySelectorAll('.category-item-container');
  if (categoryItems[itemIndex]) {
    categoryItems[itemIndex].classList.toggle('expanded');
    console.log(`Manually toggled item ${itemIndex}`);
  }
}

// Function to expand all dropdowns
function expandAllDropdowns() {
  const categoryItems = document.querySelectorAll('.category-item-container');
  categoryItems.forEach(item => {
    item.classList.add('expanded');
  });
  console.log('Expanded all dropdowns');
}

// Function to collapse all dropdowns
function collapseAllDropdowns() {
  const categoryItems = document.querySelectorAll('.category-item-container');
  categoryItems.forEach(item => {
    item.classList.remove('expanded');
  });
  console.log('Collapsed all dropdowns');
}

// Export functions for global access (if needed)
window.toggleDropdown = toggleDropdown;
window.expandAllDropdowns = expandAllDropdowns;
window.collapseAllDropdowns = collapseAllDropdowns;
