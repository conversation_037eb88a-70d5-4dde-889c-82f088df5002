/* SIMPLE SIDEBAR LIST - COPY THIS TO REPLAY HAWK CUSTOM CSS */

/* DROPDOWN FUNCTIONALITY WITH ARROW ICONS */

/* Hide only non-functional arrow icons but keep dropdown arrows */
img.w-full.rounded-lg.aspect-video.col-span-3.object-cover {
  display: none !important;
}

/* Add dropdown arrow for ALL items that could be expandable */
.flex.items-center.justify-between::after {
  content: '▶' !important;
  display: inline-block !important;
  font-size: 14px !important;
  color: #666 !important;
  transition: transform 0.3s ease !important;
  position: absolute !important;
  right: 15px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  cursor: pointer !important;
  z-index: 10 !important;
}

/* Make parent container relative for absolute positioning */
.flex.items-center.justify-between {
  position: relative !important;
}

/* Hide arrows for items that don't have sub-content */
.flex.items-center.justify-between:not([data-has-dropdown])::after {
  display: none !important;
}

/* Show arrows only for items with dropdown class */
.dropdown-item .flex.items-center.justify-between::after,
.has-dropdown .flex.items-center.justify-between::after {
  display: inline-block !important;
}

/* Rotate arrow when expanded */
.category-item-container.expanded .flex.items-center.justify-between::after,
#playlist-container .expanded .flex.items-center.justify-between::after,
.expanded > .flex.items-center.justify-between::after {
  transform: rotate(90deg) !important;
}

/* Style for sub-lessons container */
.sub-cat-bg {
  max-height: 0 !important;
  overflow: hidden !important;
  transition: max-height 0.3s ease !important;
}

/* Show sub-lessons when parent is expanded */
.category-item-container.expanded .sub-cat-bg {
  max-height: 500px !important;
}

/* Style sub-lesson items */
.sub-cat-bg .flex.items-center {
  padding: 8px 20px 8px 40px !important;
  background: #f8f9fa !important;
  border-left: 3px solid #007bff !important;
  margin: 2px 0 !important;
}

.sub-cat-bg .flex.items-center:hover {
  background: #e9ecef !important;
}

/* Make sidebar items align left but keep all text content */
#playlist-container .flex.items-center.justify-between,
.category-item-container .flex.items-center.justify-between {
  justify-content: space-between !important;
}

/* Shift specific text class to the right end */
p.text-md.m-0.custom-word-break.category-playlist-count.secondary-text {
  text-align: right !important;
  margin-left: auto !important;
}

/* Remove all card styling - target everything */
#playlist-container * {
  border-radius: 0 !important;
  box-shadow: none !important;
}

/* Main container */
#playlist-container {
  background: transparent !important;
  padding: 0 !important;
}

/* Remove card wrapper styling */
.category-playlist-wrapper,
[class*="category-playlist-wrapper"] {
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Remove all rounded, border, shadow classes */
.rounded-lg,
.border,
.shadow-sm,
.bg-white {
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

/* Style menu items as simple list */
[class*="flex items-center justify-between"] {
  background: #ffffff !important;
  border: none !important;
  border-bottom: 1px solid #e5e7eb !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 12px 20px !important;
  margin: 0 !important;
}

/* Hover effect */
[class*="flex items-center justify-between"]:hover {
  background: #f8f9fa !important;
}

/* Navigation Links - Custom styling for playlist items */
.category-playlist-wrapper .rounded-lg {
  padding: 12px 20px !important;
  color: #495057 !important;
  text-decoration: none !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  border-left: 3px solid transparent !important;
  cursor: pointer !important;
  margin: 4px 8px !important;
  border-radius: 6px !important;
}

/* Hover Effects for Replay Hawk items */
.category-playlist-wrapper .rounded-lg:hover {
  background-color: #f8f9fa !important;
  color: #007bff !important;
  border-left-color: #007bff !important;
  transform: translateX(2px) !important;
}

/* Active/Selected State */
.nav-link.active, .sidebar-link.active, .menu-link.active,
.nav-item.active .nav-link, .sidebar-item.active .sidebar-link {
  background-color: #007bff;
  color: white;
  border-left-color: #0056b3;
}

/* Icons in Navigation */
.nav-icon, .sidebar-icon, .menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  opacity: 0.7;
}

/* Expandable Menu Items */
.nav-item.expandable, .sidebar-item.expandable {
  position: relative;
}

.nav-item.expandable::after, .sidebar-item.expandable::after {
  content: '▶';
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  transition: transform 0.3s ease;
}

.nav-item.expanded::after, .sidebar-item.expanded::after {
  transform: translateY(-50%) rotate(90deg);
}

/* Sub-menu Items */
.sub-menu, .nested-menu {
  background-color: #f1f3f4;
  padding-left: 40px;
  display: none;
}

.nav-item.expanded .sub-menu, .sidebar-item.expanded .nested-menu {
  display: block;
}

.sub-menu .nav-link, .nested-menu .sidebar-link {
  padding: 10px 20px;
  font-size: 13px;
  border-left: none;
}

/* Header/Logo Area */
.sidebar-header, .nav-header {
  padding: 20px;
  border-bottom: 2px solid #e9ecef;
  margin-bottom: 10px;
}

.sidebar-logo, .nav-logo {
  max-width: 100%;
  height: auto;
}

/* Icon Wrapper Fix */
#icon-wrapper, .your-icon-container-class {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 20px;
  border-bottom: 1px solid #e9ecef;
}

/* Hamburger Icon Styling */
#app-launcher svg, .hamburger-icon {
  background-image: url('https://upload.wikimedia.org/wikipedia/commons/b/b2/Hamburger_icon.svg');
  background-size: contain;
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
  fill: none;
  display: inline-block;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

#app-launcher svg:hover, .hamburger-icon:hover {
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .category-playlist-wrapper .rounded-lg {
    margin: 2px 4px !important;
    padding: 10px 16px !important;
    font-size: 13px !important;
  }
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar, .nav-sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track, .nav-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb, .nav-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover, .nav-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Hover effects for menu items */
.flex.items-center.justify-between.h-20.px-6:hover {
  background-color: #f8f9fa !important;
  transform: translateX(2px) !important;
}

/* Text styling for menu items */
.flex.items-center.justify-between.h-20.px-6 span,
.flex.items-center.justify-between.h-20.px-6 div {
  color: #495057 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

/* Specific styling for different menu sections */
.training-section {
  background-color: #f8f9fa;
  margin: 10px 0;
}

.training-section .nav-link {
  font-weight: 600;
  color: #343a40;
}

/* Footer area of sidebar */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

/* Specific styling for different menu sections */
.training-section {
  background-color: #f8f9fa;
  margin: 10px 0;
}

.training-section .nav-link {
  font-weight: 600;
  color: #343a40;
}

/* Footer area of sidebar */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

/* ========================================= */
/* JAVASCRIPT FOR DROPDOWN FUNCTIONALITY */
/* ========================================= */
/*
JAVASCRIPT IS NOW IN SEPARATE FILE: dropdown-functionality.js

TO USE:
1. Copy the CSS above to Replay Hawk Custom CSS
2. Add this script tag to your HTML page:
   <script src="dropdown-functionality.js"></script>

OR copy the JavaScript code from dropdown-functionality.js and paste in browser console

AVAILABLE FUNCTIONS:
- toggleDropdown(index) - Toggle specific dropdown by index
- expandAllDropdowns() - Expand all dropdowns
- collapseAllDropdowns() - Collapse all dropdowns
*/