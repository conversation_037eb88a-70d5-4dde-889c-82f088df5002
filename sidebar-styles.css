/* Sidebar Navigation Styles for Replay Hawk Website */

/* Main Sidebar Container - Using actual Replay Hawk classes */
.category-playlist-wrapper, .grid.category-playlist-wrapper {
  width: 280px;
  height: 100vh;
  background-color: #FEFEFF !important;
  border-right: 1px solid #E4E4E4 !important;
  position: fixed;
  left: 0;
  top: 0;
  overflow-y: auto;
  z-index: 1000;
  padding: 20px 0;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

/* Navigation List */
.nav-list, .sidebar-menu, ul.navigation {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Navigation Items */
.nav-item, .sidebar-item, .menu-item {
  margin: 0;
  border-bottom: 1px solid #e9ecef;
}

/* Navigation Links */
.nav-link, .sidebar-link, .menu-link {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #495057;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

/* Hover Effects */
.nav-link:hover, .sidebar-link:hover, .menu-link:hover {
  background-color: #e9ecef;
  color: #007bff;
  border-left-color: #007bff;
}

/* Active/Selected State */
.nav-link.active, .sidebar-link.active, .menu-link.active,
.nav-item.active .nav-link, .sidebar-item.active .sidebar-link {
  background-color: #007bff;
  color: white;
  border-left-color: #0056b3;
}

/* Icons in Navigation */
.nav-icon, .sidebar-icon, .menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  opacity: 0.7;
}

/* Expandable Menu Items */
.nav-item.expandable, .sidebar-item.expandable {
  position: relative;
}

.nav-item.expandable::after, .sidebar-item.expandable::after {
  content: '▶';
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  transition: transform 0.3s ease;
}

.nav-item.expanded::after, .sidebar-item.expanded::after {
  transform: translateY(-50%) rotate(90deg);
}

/* Sub-menu Items */
.sub-menu, .nested-menu {
  background-color: #f1f3f4;
  padding-left: 40px;
  display: none;
}

.nav-item.expanded .sub-menu, .sidebar-item.expanded .nested-menu {
  display: block;
}

.sub-menu .nav-link, .nested-menu .sidebar-link {
  padding: 10px 20px;
  font-size: 13px;
  border-left: none;
}

/* Header/Logo Area */
.sidebar-header, .nav-header {
  padding: 20px;
  border-bottom: 2px solid #e9ecef;
  margin-bottom: 10px;
}

.sidebar-logo, .nav-logo {
  max-width: 100%;
  height: auto;
}

/* Icon Wrapper Fix */
#icon-wrapper, .your-icon-container-class {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 20px;
  border-bottom: 1px solid #e9ecef;
}

/* Hamburger Icon Styling */
#app-launcher svg, .hamburger-icon {
  background-image: url('https://upload.wikimedia.org/wikipedia/commons/b/b2/Hamburger_icon.svg');
  background-size: contain;
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
  fill: none;
  display: inline-block;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

#app-launcher svg:hover, .hamburger-icon:hover {
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar, .nav-sidebar, .navigation-panel {
    width: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open, .nav-sidebar.open, .navigation-panel.open {
    transform: translateX(0);
  }
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar, .nav-sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track, .nav-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb, .nav-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover, .nav-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Additional Classes for Common Elements */
.welcome-item, .start-here-item, .certified-item, .thinking-results-item,
.lead-field-item, .sales-training-item, .client-activations-item {
  /* Inherit from nav-item styles above */
}

/* Specific styling for different menu sections */
.training-section {
  background-color: #f8f9fa;
  margin: 10px 0;
}

.training-section .nav-link {
  font-weight: 600;
  color: #343a40;
}

/* Footer area of sidebar */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}
